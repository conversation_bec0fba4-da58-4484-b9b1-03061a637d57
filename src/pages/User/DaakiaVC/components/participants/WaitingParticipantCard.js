/* eslint-disable no-unused-vars */
import React, { useState, useEffect, useCallback } from "react";
import { Ava<PERSON>, Button } from "antd";

import "./styles/ParticipantCard.scss";
import { truncateString,generateAvatar, getParticipantColor } from "../../utils/helper";
import { APIrequest } from "../../API/axios";
import { Endpoints } from "../../API/Endpoints/routes";

function UserInfo({ name, avatarName, truncatedName, children, participantColors = new Map(), participantIdentity }) {
  // Helper function to get participant color with fallback
  const getAvatarColor = (identity) => {
    if (participantColors && identity) {
      const color = getParticipantColor(participantColors, identity);
      return color || "#fd4563"; // fallback to original color
    }
    return "#fd4563"; // fallback to original color
  };

  return (
    <div title={name} className="user-info">
      <Avatar style={{ backgroundColor: getAvatarColor(participantIdentity), verticalAlign: "middle" }}>
        {avatarName}
      </Avatar>
      <div className="wpc-user-details">
        <div className="username">{truncatedName}</div>
        {children}
      </div>
    </div>
  );
}



export function WaitingParticipantCard({
  participant,
  localParticipant,
  raisedHand,
  id,
  isHost,
  lobbyParticipants,
  setLobbyParticipants,
  coHostToken,
  setToastNotification,
  setToastStatus,
  setShowToast,
  participantColors = new Map(),
  removeParticipantFromLobby,
}) {
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // const isSelf = participant.isLocal ? " (You)" : "";

  const userInfo = {
    truncatedName: truncateString(participant.display_name),
    name: participant.display_name, // Assuming isSelf is a boolean indicating the current user
    avatarName: generateAvatar(participant.display_name),
    participantColors,
    participantIdentity: participant.request_id, // Using request_id as identifier for waiting participants
  };

  // const actionIcons = [];

  // if (participant.isCameraEnabled) {
  //   actionIcons.push(<VideoOn />);
  // } else {
  //   actionIcons.push(<VideoOff />);
  // }

  // if (participant.isMicrophoneEnabled) {
  //   actionIcons.push(<MicrophoneOpenIcon />);
  // } else {
  //   actionIcons.push(<MicrophoneCloseIcon className="pt-mic-off-ico" />);
  // }

  // if (!participant.isLocal) {
  //   actionIcons.push(
  //     <ParticipantsPopOver
  //       participant={participant}
  //       localParticipant={localParticipant}
  //       meetingUid={id}
  //       isHost={isHost}
  //     />
  //   );
  // }

  const handleStatus = useCallback(
    async (status) => {
      try {
        const response = await APIrequest({
          method: Endpoints.status_update_participant_lobby.method,
          endpoint: Endpoints.status_update_participant_lobby.url,
          payload: {
            meeting_uid: id,
            request_id: participant.request_id,
            is_admit: status,
          },
          token: coHostToken,
        });
        if (response.success === 0) {
          setToastNotification("Error updating participant status.");
          setToastStatus("error");
          setShowToast(true);
        }

        // Use the removeParticipantFromLobby function
        if (removeParticipantFromLobby) {
          removeParticipantFromLobby(participant.request_id);
        }
      } catch (error) {
        setToastNotification(error.message);
        setToastStatus("error");
        setShowToast(true);
        // console.error("Error updating participant status", error);
      }
    },
    [participant, removeParticipantFromLobby, id, coHostToken, setToastNotification, setToastStatus, setShowToast]
  );

  return (
    <article className="post-card-waiting">
    <UserInfo {...userInfo}>
      <div className="wpc-buttons">
        <Button type="text" onClick={() => handleStatus(false)}>
          Deny
        </Button>
        <Button type="text" onClick={() => handleStatus(true)}>
          Allow
        </Button>
      </div>
    </UserInfo>
    {/* <div className="wpc-user-details">
      <div className="username">{userInfo.truncatedName}</div>
      <div className="wpc-buttons">
        <Button type="text" onClick={() => handleStatus(false)}>
          Deny
        </Button>
        <Button type="text" onClick={() => handleStatus(true)}>
          Allow
        </Button>
      </div>
    </div> */}
  </article>
  );
}
